#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证和展示医疗体检套餐价格推导结果
"""

import json
import pandas as pd

def main():
    """验证结果"""
    print("=== 医疗体检套餐价格分析结果 ===\n")
    
    # 加载数据
    with open('data.json', 'r', encoding='utf-8') as f:
        data_json = json.load(f)
    
    with open('price.json', 'r', encoding='utf-8') as f:
        price_json = json.load(f)
    
    data_result = data_json.get('result', [])
    price_result = price_json.get('result', [])
    
    print(f"数据概览:")
    print(f"- 套餐内容数据: {len(data_result)} 条")
    print(f"- 价格数据: {len(price_result)} 条")
    
    # 分析价格数据
    price_map = {}
    for item in price_result:
        name = item['name']
        cost = item['packageCost']
        marital = item['packageMarital']  # 1=未婚, 2=已婚, 3=其他
        sex = item['packageSex']  # 1=男, 2=女
        price_map[name] = {
            'cost': cost,
            'marital': marital,
            'sex': sex
        }
    
    print(f"\n价格分析:")
    
    # 找到女已婚和女未婚的组合套餐
    married_combinations = []
    unmarried_combinations = []
    
    for name, info in price_map.items():
        if info['sex'] == 2:  # 女性
            if '女已婚基础套餐' in name and info['marital'] == 2:
                if '+' in name:
                    optional_item = name.split('+')[0]
                    married_combinations.append({
                        'optional': optional_item,
                        'total_cost': info['cost'],
                        'name': name
                    })
            elif '女未婚基础套餐' in name and info['marital'] == 1:
                if '+' in name:
                    optional_item = name.split('+')[0]
                    unmarried_combinations.append({
                        'optional': optional_item,
                        'total_cost': info['cost'],
                        'name': name
                    })
    
    print(f"- 女已婚组合套餐: {len(married_combinations)} 个")
    print(f"- 女未婚组合套餐: {len(unmarried_combinations)} 个")
    
    # 计算价格差异
    price_differences = []
    print(f"\n价格对比分析:")
    for married in married_combinations:
        for unmarried in unmarried_combinations:
            if married['optional'] == unmarried['optional']:
                diff = married['total_cost'] - unmarried['total_cost']
                price_differences.append(diff)
                print(f"- {married['optional']}: 已婚 {married['total_cost']:.2f}元, 未婚 {unmarried['total_cost']:.2f}元, 差异 {diff:.2f}元")
    
    if price_differences:
        avg_diff = sum(price_differences) / len(price_differences)
        print(f"\n基础套餐平均价格差异: {avg_diff:.2f}元")
        
        # 推导基础套餐价格
        if married_combinations and unmarried_combinations:
            # 使用最低价格的组合套餐来推导
            min_married = min(married_combinations, key=lambda x: x['total_cost'])
            min_unmarried = min(unmarried_combinations, key=lambda x: x['total_cost'])
            
            # 假设最简单的自选项价格为100元
            estimated_optional_price = 100
            
            married_base = min_married['total_cost'] - estimated_optional_price
            unmarried_base = min_unmarried['total_cost'] - estimated_optional_price
            
            print(f"\n推导结果:")
            print(f"- 女已婚基础套餐价格: {married_base:.2f}元")
            print(f"- 女未婚基础套餐价格: {unmarried_base:.2f}元")
            print(f"- 价格差异: {married_base - unmarried_base:.2f}元")
            
            print(f"\n推导依据:")
            print(f"- 参考套餐(已婚): {min_married['name']} - {min_married['total_cost']:.2f}元")
            print(f"- 参考套餐(未婚): {min_unmarried['name']} - {min_unmarried['total_cost']:.2f}元")
            print(f"- 估算自选项价格: {estimated_optional_price}元")
    
    # 检查Excel文件是否生成
    try:
        excel_file = '医疗体检套餐分析报告.xlsx'
        df = pd.read_excel(excel_file, sheet_name=None)
        print(f"\nExcel报告生成成功: {excel_file}")
        print(f"包含工作表: {list(df.keys())}")
        
        # 显示统计汇总
        if '统计汇总' in df:
            summary_df = df['统计汇总']
            print(f"\n统计汇总:")
            for _, row in summary_df.iterrows():
                print(f"- {row['项目']}: {row['值']}")
                
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
    
    print(f"\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
